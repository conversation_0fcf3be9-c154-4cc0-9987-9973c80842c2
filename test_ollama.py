import ollama

# Connect to Ollama
client = ollama.Client(host='http://***********:11434')

# Source topics to validate
source_topics = [
    "Introduction",
    "Objectives",
    "Scope",
    "Definitions",
    "Disaster Scenarios",
    "Recovery Strategies",
    "Roles and Responsibilities",
    "Communication Plan",
    "Testing and Maintenance",
    "Conclusion"
]

# Input BCM/DR document
input_doc = """
Title: Partial Disaster Recovery Plan – Phase 1

1. Introduction  
This document presents a preliminary Disaster Recovery (DR) plan aimed at protecting critical IT systems during emergencies.  
It describes the importance of DR in minimizing downtime and financial loss.  
The content is based on an internal risk audit conducted in Q1.  
The document is intended for IT leadership and infrastructure teams.  
It serves as the foundation for a more comprehensive DR strategy in future phases.

2. Objectives  
The primary objective is to ensure business continuity in the event of a data center failure or major cyber incident.  
Another goal is to provide a framework for restoring services within the defined Recovery Time Objective (RTO).  
The document also aims to identify mission-critical systems and prioritize them.  
It sets measurable goals for recovery planning and implementation.  
Overall, it helps prepare the organization to handle IT disruptions with minimal impact.

3. Scope  
This plan covers only the primary data center and excludes branch office IT systems.  
It includes network hardware, application servers, databases, and cloud service integrations.  
The scope is limited to Tier 1 applications and does not include user-end recovery plans.  
Only infrastructure-related systems that affect operations directly are considered.  
The next version will expand to include user access and end-device restoration.

4. Recovery Strategies  
The plan outlines cold site activation as the primary backup strategy.  
A detailed failover process is included for critical database systems.  
Cloud backups are scheduled daily with a 7-day retention policy.  
Virtual machine replication ensures high availability for ERP systems.  
These strategies are selected based on cost, reliability, and risk level.

5. Conclusion  
This phase establishes the groundwork for a full-fledged disaster recovery system.  
It highlights the gaps identified during the internal audit and the first steps toward bridging them.  
The team acknowledges that additional testing and policy development are pending.  
Further revisions will address communication protocols and role assignments.  
Management approval and funding will determine the plan’s expansion in Phase 2.
"""

# Clear criteria
criteria = """
Classification Definitions:
- Fully Covered: All key points under the topic are clearly and completely addressed.
- Partially Covered: Some relevant points are mentioned, but the coverage is incomplete or vague.
- Not Covered: The topic is not addressed or cannot be inferred from the content.
"""

# Analyze each topic individually
for topic in source_topics:
    prompt = f"""
You are an expert in Business Continuity and Disaster Recovery document analysis.

{criteria}

Evaluate whether the following topic is fully covered, partially covered, or not covered in the document below.

Topic: {topic}

Document:
{input_doc}

Respond in the format:
- Verdict: <Fully Covered / Partially Covered / Not Covered>
- Reason: <One sentence reason>
"""
    response = client.chat(model='llama3.2', messages=[{'role': 'user', 'content': prompt}])
    print(f"\n📌 Topic: {topic}")
    print(response['message']['content'])
