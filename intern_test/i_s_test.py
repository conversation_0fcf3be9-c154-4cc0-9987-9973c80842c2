from flask import Flask, render_template, request, jsonify, redirect
import os
from werkzeug.utils import secure_filename
from langchain_community.document_loaders import PyPDFLoader
from langchain_ollama import OllamaEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_ollama import ChatOllama
from langchain_core.runnables import RunnablePassthrough
from langchain.retrievers.multi_query import MultiQueryRetriever
import ollama

app = Flask(__name__)

# === Config ===
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# === Global Variables ===
model = "llama3.2"
embeddings = None
llm = None
retriever = None  # Changed from retriever_chain to retriever
models_initialized = False


# === Model Initialization ===
@app.before_request
def initialize_models():
    global embeddings, llm, models_initialized
    if not models_initialized:
        print("⚡ Pre-loading models...")
        try:
            ollama.pull("nomic-embed-text")
            embeddings = OllamaEmbeddings(model="nomic-embed-text")
            llm = ChatOllama(model=model)
            models_initialized = True
            print(" Models ready")
        except Exception as e:
            print(f" Model loading failed: {str(e)}")


# === Helper Functions ===
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def build_retriever(filepath):
    try:
        print(" Loading document...")
        loader = PyPDFLoader(filepath)
        data = loader.load_and_split()

        print("Splitting text...")
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=800,
            chunk_overlap=150,
            separators=["\n\n", "\n", " "]
        )
        chunks = text_splitter.split_documents(data)

        print(" Creating vector store...")
        vectorstore = Chroma.from_documents(
            documents=chunks,
            embedding=embeddings,
            collection_name=f"bcm-{os.path.basename(filepath)}"
        )
        return vectorstore.as_retriever()  # Convert to retriever interface
    except Exception as e:
        print(f"🔥 Error in build_retriever: {str(e)}")
        raise


def build_qa_chain(retriever):
    try:
        prompt = ChatPromptTemplate.from_template("""
        You are a BCM compliance expert. Answer clearly and precisely.

        Context: {context}
        Question: {question}
        """)

        return (
                {"context": retriever, "question": RunnablePassthrough()}
                | prompt
                | llm
                | StrOutputParser()
        )
    except Exception as e:
        print(f"🔥 Error in build_qa_chain: {str(e)}")
        raise


# === Routes ===
@app.route('/', methods=['GET', 'POST'])
def upload_file():
    global retriever

    if request.method == 'POST':
        if 'file' not in request.files:
            return redirect(request.url)

        file = request.files['file']
        if not file or file.filename == '':
            return redirect(request.url)

        if allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            try:
                # Build the retriever
                retriever = build_retriever(filepath)

                # Build the QA chain
                qa_chain = build_qa_chain(retriever)

                # Get initial analysis
                result = qa_chain.invoke("Identify gaps in this BCM document against ISO 22301 standards.")
                return render_template('results.html', result=result)
            except Exception as e:
                return render_template('upload.html', error=str(e))
            finally:
                if os.path.exists(filepath):
                    os.remove(filepath)

    return render_template('upload.html')


@app.route('/chat', methods=['POST'])
def chat():
    global retriever

    if not retriever:
        return jsonify({"error": "Please upload a document first"}), 400

    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data received"}), 400

        question = data.get('question', '').strip()
        if not question:
            return jsonify({"error": "Empty question"}), 400

        # Rebuild the QA chain for each question to ensure freshness
        qa_chain = build_qa_chain(retriever)
        response = qa_chain.invoke(question)
        return jsonify({"response": response})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)