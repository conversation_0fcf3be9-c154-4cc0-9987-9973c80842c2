<!DOCTYPE html>
<html>
<head>
    <title>Analysis Results</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #2c3e50; }
        .result-box {
            background: #f8f9fa;
            padding: 20px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .chat-box { margin-top: 30px; }
        .chat-input {
            width: 100%;
            padding: 10px;
            margin-top: 10px;
            min-height: 80px;
            resize: vertical;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            margin-top: 10px;
            border-radius: 4px;
        }
        .btn:hover { background: #2980b9; }
        .chat-response {
            background: #eef;
            padding: 15px;
            margin-top: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .error { color: #e74c3c; }
        #loading { display: none; margin-top: 10px; }
        .char-count {
            font-size: 0.8em;
            color: #666;
            text-align: right;
        }
        .analysis-title {
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>BCM Document Analysis Results</h1>
    <div class="result-box">
        <div class="analysis-title">Initial Compliance Analysis:</div>
        <div id="analysis-result">{{ result }}</div>
    </div>

    <div class="chat-box">
        <h2>Document Q&A</h2>
        <textarea id="user-question" class="chat-input"
                  placeholder="Ask about gaps, compliance, or specific sections..."
                  oninput="updateCharCount()"></textarea>
        <div class="char-count"><span id="char-count">0</span>/500 characters</div>
        <button onclick="askQuestion()" class="btn">Ask Question</button>
        <div id="loading">⏳ Processing your question...</div>
        <div id="chat-response" class="chat-response"></div>
    </div>

    <a href="/" class="btn">Analyze Another Document</a>

    <script>
        function updateCharCount() {
            const question = document.getElementById('user-question').value;
            document.getElementById('char-count').textContent = question.length;
        }

        function askQuestion() {
            const question = document.getElementById('user-question').value.trim();
            const responseBox = document.getElementById('chat-response');
            const loading = document.getElementById('loading');

            if (!question) {
                responseBox.innerHTML = '<span class="error">Please enter a question</span>';
                return;
            }

            if (question.length > 500) {
                responseBox.innerHTML = '<span class="error">Question too long (max 500 characters)</span>';
                return;
            }

            responseBox.innerHTML = '';
            loading.style.display = 'block';

            fetch('/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ question: question })
            })
            .then(response => {
                loading.style.display = 'none';
                if (!response.ok) throw new Error('Network error');
                return response.json();
            })
            .then(data => {
                if (data.error) throw new Error(data.error);
                responseBox.textContent = data.response;
            })
            .catch(error => {
                loading.style.display = 'none';
                responseBox.innerHTML = `<span class="error">Error: ${error.message}</span>`;
            });
        }

        // Focus the question input when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('user-question').focus();
        });
    </script>
</body>
</html>