"""
BCM Compliance Checker - Single File Solution
Compares BCM ISO topics from Word document against company BCM plan PDF using Ollama
"""

import ollama
import json
from pathlib import Path
import re
from typing import Dict, List, Tuple

# Document processing imports
try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Please install PyPDF2 or pypdf: pip install PyPDF2")
        exit(1)

try:
    from docx import Document
except ImportError:
    print("Please install python-docx: pip install python-docx")
    exit(1)


class BCMComplianceChecker:
    def __init__(self):
        """Initialize the BCM Compliance Checker"""
        self.client = ollama.Client(host='http://localhost:11434')
        self.model = 'llama3.2:latest'
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF file"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            print(f"Error reading PDF: {e}")
            return ""
    
    def extract_text_from_docx(self, docx_path: str) -> str:
        """Extract text from Word document"""
        try:
            doc = Document(docx_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error reading Word document: {e}")
            return ""
    
    def extract_iso_topics(self, framework_text: str) -> Dict[str, List[str]]:
        """Extract ISO topics and subtopics from framework document using AI"""
        prompt = f"""
        Analyze the following BCM framework document and extract all ISO 22301 topics and their subtopics.
        
        Return the result as a JSON object where each main topic is a key and its subtopics are in a list.
        
        Example format:
        {{
            "Context of the Organization": [
                "Understanding the organization and its context",
                "Understanding the needs and expectations of interested parties",
                "Determining the scope of the business continuity management system"
            ],
            "Leadership": [
                "Leadership and commitment",
                "Policy",
                "Organizational roles, responsibilities and authorities"
            ]
        }}
        
        Framework document:
        {framework_text[:8000]}
        
        Return only valid JSON, no additional text.
        """
        
        try:
            response = self.client.chat(
                model=self.model,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            # Extract JSON from response
            response_text = response['message']['content']
            
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                print("Could not extract JSON from AI response")
                return {}
                
        except Exception as e:
            print(f"Error extracting ISO topics: {e}")
            return {}
    
    def check_topic_coverage(self, topic: str, subtopics: List[str], company_plan: str) -> Dict:
        """Check if a specific ISO topic is covered in the company plan"""
        subtopics_text = "\n".join([f"- {subtopic}" for subtopic in subtopics])
        
        prompt = f"""
        You are a BCM compliance expert. Analyze if the following ISO 22301 topic and its subtopics are covered in the company's BCM plan.
        
        ISO Topic: {topic}
        Subtopics to check:
        {subtopics_text}
        
        For the main topic and each subtopic, determine:
        - "Fully Covered": Topic is comprehensively addressed
        - "Partially Covered": Topic is mentioned but lacks detail
        - "Not Covered": Topic is not mentioned or addressed
        
        Company BCM Plan:
        {company_plan[:6000]}
        
        Return your analysis as JSON in this exact format:
        {{
            "topic": "{topic}",
            "overall_status": "Fully Covered/Partially Covered/Not Covered",
            "subtopic_analysis": [
                {{
                    "subtopic": "subtopic name",
                    "status": "Fully Covered/Partially Covered/Not Covered",
                    "evidence": "brief explanation or quote from document"
                }}
            ],
            "recommendations": "suggestions for improvement if needed"
        }}
        
        Return only valid JSON, no additional text.
        """
        
        try:
            response = self.client.chat(
                model=self.model,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            response_text = response['message']['content']
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                return {
                    "topic": topic,
                    "overall_status": "Analysis Failed",
                    "subtopic_analysis": [],
                    "recommendations": "Could not analyze this topic"
                }
                
        except Exception as e:
            print(f"Error checking topic coverage for {topic}: {e}")
            return {
                "topic": topic,
                "overall_status": "Analysis Failed",
                "subtopic_analysis": [],
                "recommendations": f"Error: {str(e)}"
            }
    
    def generate_compliance_report(self, results: List[Dict]) -> str:
        """Generate a comprehensive compliance report"""
        report = []
        report.append("=" * 80)
        report.append("BCM ISO 22301 COMPLIANCE ANALYSIS REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Summary statistics
        total_topics = len(results)
        fully_covered = sum(1 for r in results if r['overall_status'] == 'Fully Covered')
        partially_covered = sum(1 for r in results if r['overall_status'] == 'Partially Covered')
        not_covered = sum(1 for r in results if r['overall_status'] == 'Not Covered')
        
        report.append("📊 SUMMARY")
        report.append("-" * 40)
        report.append(f"Total ISO Topics Analyzed: {total_topics}")
        report.append(f"✅ Fully Covered: {fully_covered}")
        report.append(f"⚠️  Partially Covered: {partially_covered}")
        report.append(f"❌ Not Covered: {not_covered}")
        report.append(f"📈 Compliance Rate: {(fully_covered/total_topics)*100:.1f}%")
        report.append("")
        
        # Detailed analysis
        report.append("📋 DETAILED ANALYSIS")
        report.append("-" * 40)
        
        for result in results:
            status_icon = {
                'Fully Covered': '✅',
                'Partially Covered': '⚠️',
                'Not Covered': '❌',
                'Analysis Failed': '🔥'
            }.get(result['overall_status'], '❓')
            
            report.append(f"\n{status_icon} {result['topic']}")
            report.append(f"   Status: {result['overall_status']}")
            
            if result['subtopic_analysis']:
                report.append("   Subtopics:")
                for subtopic in result['subtopic_analysis']:
                    sub_icon = {
                        'Fully Covered': '✅',
                        'Partially Covered': '⚠️',
                        'Not Covered': '❌'
                    }.get(subtopic['status'], '❓')
                    report.append(f"     {sub_icon} {subtopic['subtopic']}")
                    if subtopic.get('evidence'):
                        report.append(f"        Evidence: {subtopic['evidence'][:100]}...")
            
            if result.get('recommendations'):
                report.append(f"   💡 Recommendations: {result['recommendations']}")
        
        return "\n".join(report)
    
    def run_compliance_check(self, framework_path: str, company_plan_path: str):
        """Main function to run the complete compliance check"""
        print("🚀 Starting BCM ISO 22301 Compliance Check...")
        print("=" * 60)
        
        # Step 1: Extract documents
        print("📄 Extracting BCM Framework (ISO topics)...")
        framework_text = self.extract_text_from_docx(framework_path)
        if not framework_text:
            print("❌ Failed to extract framework document")
            return
        
        print("📄 Extracting Company BCM Plan...")
        company_text = self.extract_text_from_pdf(company_plan_path)
        if not company_text:
            print("❌ Failed to extract company BCM plan")
            return
        
        # Step 2: Extract ISO topics
        print("🧠 Analyzing ISO topics from framework...")
        iso_topics = self.extract_iso_topics(framework_text)
        if not iso_topics:
            print("❌ Failed to extract ISO topics")
            return
        
        print(f"✅ Found {len(iso_topics)} ISO topics")
        
        # Step 3: Check each topic
        print("🔍 Checking topic coverage...")
        results = []
        
        for i, (topic, subtopics) in enumerate(iso_topics.items(), 1):
            print(f"   Analyzing {i}/{len(iso_topics)}: {topic}")
            result = self.check_topic_coverage(topic, subtopics, company_text)
            results.append(result)
        
        # Step 4: Generate report
        print("📊 Generating compliance report...")
        report = self.generate_compliance_report(results)
        
        # Save report
        report_file = "bcm_compliance_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Report saved to: {report_file}")
        print("\n" + "=" * 60)
        print("📋 QUICK SUMMARY:")
        print("=" * 60)
        
        # Print summary
        lines = report.split('\n')
        summary_start = False
        for line in lines:
            if "📊 SUMMARY" in line:
                summary_start = True
            elif "📋 DETAILED ANALYSIS" in line:
                break
            elif summary_start and line.strip():
                print(line)


def main():
    """Main execution function"""
    # File paths
    framework_file = "BCM_Plan_Framework.docx"
    company_plan_file = "Perpetuuiti_BCM_Plan.pdf"
    
    # Check if files exist
    if not Path(framework_file).exists():
        print(f"❌ Framework file not found: {framework_file}")
        return
    
    if not Path(company_plan_file).exists():
        print(f"❌ Company plan file not found: {company_plan_file}")
        return
    
    # Run compliance check
    checker = BCMComplianceChecker()
    checker.run_compliance_check(framework_file, company_plan_file)


if __name__ == "__main__":
    main()
