"""
Quick BCM Compliance Checker - Optimized for Speed
Compares BCM ISO topics against company BCM plan using Ollama
"""

import ollama
from pathlib import Path

# Document processing imports
try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Please install PyPDF2: pip install PyPDF2")
        exit(1)

try:
    from docx import Document
except ImportError:
    print("Please install python-docx: pip install python-docx")
    exit(1)


def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
            return text.strip()
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return ""


def extract_text_from_docx(docx_path):
    """Extract text from Word document"""
    try:
        doc = Document(docx_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error reading Word document: {e}")
        return ""


def analyze_with_ollama(prompt):
    """Send prompt to Ollama and get response"""
    try:
        client = ollama.Client(host='http://localhost:11434')
        response = client.chat(
            model='llama3.2:latest',
            messages=[{'role': 'user', 'content': prompt}]
        )
        return response['message']['content']
    except Exception as e:
        print(f"Error with Ollama: {e}")
        return f"Error: {e}"


def get_predefined_iso_topics():
    """Return predefined ISO 22301 topics to speed up analysis"""
    return {
        "Context of the Organization": [
            "Understanding the organization and its context",
            "Understanding needs and expectations of interested parties",
            "Determining scope of BCMS",
            "Business continuity management system"
        ],
        "Leadership": [
            "Leadership and commitment",
            "Policy",
            "Organizational roles, responsibilities and authorities"
        ],
        "Planning": [
            "Actions to address risks and opportunities",
            "Business continuity objectives and planning",
            "Business impact analysis and risk assessment"
        ],
        "Support": [
            "Resources",
            "Competence",
            "Awareness",
            "Communication",
            "Documented information"
        ],
        "Operation": [
            "Operational planning and control",
            "Business impact analysis",
            "Risk assessment",
            "Business continuity strategy",
            "Business continuity procedures",
            "Exercise programme"
        ],
        "Performance Evaluation": [
            "Monitoring, measurement, analysis and evaluation",
            "Internal audit",
            "Management review"
        ],
        "Improvement": [
            "Nonconformity and corrective action",
            "Continual improvement"
        ]
    }


def quick_topic_check(topic, subtopics, company_plan_sample):
    """Quick check if topic is covered - uses smaller text sample"""
    prompt = f"""
    Check if this ISO 22301 topic is covered in the BCM plan sample.
    
    Topic: {topic}
    Key subtopics: {', '.join(subtopics[:3])}  
    
    BCM Plan Sample:
    {company_plan_sample}
    
    Answer with one of:
    - FULLY COVERED: Topic comprehensively addressed
    - PARTIALLY COVERED: Topic mentioned but incomplete
    - NOT COVERED: Topic not found
    
    Give a brief 1-2 sentence explanation.
    """
    
    return analyze_with_ollama(prompt)


def main():
    """Main execution function"""
    print("🚀 Quick BCM ISO 22301 Compliance Checker")
    print("=" * 50)
    
    # File paths
    framework_file = "BCM_Plan_Framework.docx"
    company_plan_file = "Perpetuuiti_BCM_Plan.pdf"
    
    # Check if files exist
    if not Path(framework_file).exists():
        print(f"❌ Framework file not found: {framework_file}")
        return
    
    if not Path(company_plan_file).exists():
        print(f"❌ Company plan file not found: {company_plan_file}")
        return
    
    # Extract company plan
    print("📄 Extracting Company BCM Plan...")
    company_text = extract_text_from_pdf(company_plan_file)
    if not company_text:
        print("❌ Failed to extract company BCM plan")
        return
    
    print(f"✅ Company Plan: {len(company_text)} characters")
    
    # Use predefined ISO topics for speed
    print("📋 Using ISO 22301 standard topics...")
    iso_topics = get_predefined_iso_topics()
    
    print(f"✅ Analyzing {len(iso_topics)} ISO topics")
    
    # Prepare report
    report_lines = []
    report_lines.append("BCM ISO 22301 QUICK COMPLIANCE CHECK")
    report_lines.append("=" * 50)
    report_lines.append(f"Company Plan: {company_plan_file}")
    report_lines.append(f"Analysis Date: {Path().cwd()}")
    report_lines.append("")
    
    # Quick analysis using smaller text chunks
    company_sample = company_text[:2000]  # Use first 2000 chars for quick analysis
    
    print("\n🔍 Quick topic analysis...")
    
    summary_stats = {"FULLY COVERED": 0, "PARTIALLY COVERED": 0, "NOT COVERED": 0}
    
    for i, (topic, subtopics) in enumerate(iso_topics.items(), 1):
        print(f"   {i}/{len(iso_topics)}: {topic}")
        
        # Quick analysis
        analysis = quick_topic_check(topic, subtopics, company_sample)
        
        # Extract status
        status = "UNKNOWN"
        if "FULLY COVERED" in analysis.upper():
            status = "FULLY COVERED"
        elif "PARTIALLY COVERED" in analysis.upper():
            status = "PARTIALLY COVERED"
        elif "NOT COVERED" in analysis.upper():
            status = "NOT COVERED"
        
        summary_stats[status] = summary_stats.get(status, 0) + 1
        
        # Add to report
        status_icon = {"FULLY COVERED": "✅", "PARTIALLY COVERED": "⚠️", "NOT COVERED": "❌"}.get(status, "❓")
        
        report_lines.append(f"{status_icon} {topic}")
        report_lines.append(f"   Status: {status}")
        report_lines.append(f"   Analysis: {analysis}")
        report_lines.append(f"   Subtopics: {', '.join(subtopics)}")
        report_lines.append("")
    
    # Add summary
    total = len(iso_topics)
    compliance_rate = (summary_stats["FULLY COVERED"] / total) * 100
    
    summary = [
        "",
        "📊 SUMMARY",
        "=" * 30,
        f"Total Topics: {total}",
        f"✅ Fully Covered: {summary_stats['FULLY COVERED']}",
        f"⚠️ Partially Covered: {summary_stats['PARTIALLY COVERED']}",
        f"❌ Not Covered: {summary_stats['NOT COVERED']}",
        f"📈 Compliance Rate: {compliance_rate:.1f}%",
        "",
        "💡 RECOMMENDATIONS:",
        "- Review topics marked as 'NOT COVERED' or 'PARTIALLY COVERED'",
        "- Enhance documentation for partially covered areas",
        "- Consider full document analysis for detailed assessment"
    ]
    
    report_lines.extend(summary)
    
    # Save report
    report_content = "\n".join(report_lines)
    report_file = "bcm_quick_compliance_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    # Display results
    print(f"\n✅ Quick analysis complete!")
    print("=" * 50)
    print("📊 RESULTS SUMMARY:")
    print("=" * 50)
    
    for line in summary:
        print(line)
    
    print(f"\n📄 Full report saved to: {report_file}")
    
    # Show critical gaps
    if summary_stats["NOT COVERED"] > 0:
        print(f"\n⚠️ ATTENTION: {summary_stats['NOT COVERED']} topics not covered!")
        print("Review the full report for details.")


if __name__ == "__main__":
    main()
