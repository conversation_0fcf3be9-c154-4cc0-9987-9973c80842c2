"""
Simple BCM Compliance Checker - Works with Ollama
Compares BCM ISO topics from Word document against company BCM plan PDF
"""

import ollama
import json
from pathlib import Path
import re

# Document processing imports
try:
    from PyPDF2 import PdfReader
except ImportError:
    try:
        from pypdf import PdfReader
    except ImportError:
        print("Please install PyPDF2: pip install PyPDF2")
        exit(1)

try:
    from docx import Document
except ImportError:
    print("Please install python-docx: pip install python-docx")
    exit(1)


def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
            return text.strip()
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return ""


def extract_text_from_docx(docx_path):
    """Extract text from Word document"""
    try:
        doc = Document(docx_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error reading Word document: {e}")
        return ""


def analyze_with_ollama(prompt):
    """Send prompt to Ollama and get response"""
    try:
        client = ollama.Client(host='http://localhost:11434')
        response = client.chat(
            model='llama3.2:latest',
            messages=[{'role': 'user', 'content': prompt}]
        )
        return response['message']['content']
    except Exception as e:
        print(f"Error with Ollama: {e}")
        return None


def extract_iso_topics(framework_text):
    """Extract ISO topics from framework document"""
    prompt = f"""
    Extract all BCM ISO 22301 topics and subtopics from this framework document.
    
    List them in this simple format:
    
    TOPIC: [Main Topic Name]
    - Subtopic 1
    - Subtopic 2
    - Subtopic 3
    
    TOPIC: [Next Topic Name]
    - Subtopic 1
    - Subtopic 2
    
    Framework document:
    {framework_text[:4000]}
    
    Focus on ISO 22301 standard topics like:
    - Context of the Organization
    - Leadership
    - Planning
    - Support
    - Operation
    - Performance Evaluation
    - Improvement
    """
    
    response = analyze_with_ollama(prompt)
    if not response:
        return {}
    
    # Parse the response to extract topics
    topics = {}
    current_topic = None
    
    for line in response.split('\n'):
        line = line.strip()
        if line.startswith('TOPIC:'):
            current_topic = line.replace('TOPIC:', '').strip()
            topics[current_topic] = []
        elif line.startswith('-') and current_topic:
            subtopic = line.replace('-', '').strip()
            if subtopic:
                topics[current_topic].append(subtopic)
    
    return topics


def check_topic_coverage(topic, subtopics, company_plan):
    """Check if topic is covered in company plan"""
    subtopics_text = "\n".join([f"- {subtopic}" for subtopic in subtopics])
    
    prompt = f"""
    Analyze if this ISO 22301 topic is covered in the company BCM plan.
    
    ISO Topic: {topic}
    Subtopics to check:
    {subtopics_text}
    
    Company BCM Plan (first 3000 characters):
    {company_plan[:3000]}
    
    For each subtopic, respond with:
    - FULLY COVERED: if comprehensively addressed
    - PARTIALLY COVERED: if mentioned but lacks detail  
    - NOT COVERED: if not mentioned
    
    Format your response as:
    TOPIC: {topic}
    OVERALL: [FULLY COVERED/PARTIALLY COVERED/NOT COVERED]
    
    SUBTOPIC ANALYSIS:
    - [Subtopic name]: [STATUS] - [brief explanation]
    - [Subtopic name]: [STATUS] - [brief explanation]
    
    RECOMMENDATIONS: [suggestions if needed]
    """
    
    response = analyze_with_ollama(prompt)
    return response if response else f"Analysis failed for {topic}"


def main():
    """Main execution function"""
    print("🚀 BCM ISO 22301 Compliance Checker")
    print("=" * 50)
    
    # File paths
    framework_file = "BCM_Plan_Framework.docx"
    company_plan_file = "Perpetuuiti_BCM_Plan.pdf"
    
    # Check if files exist
    if not Path(framework_file).exists():
        print(f"❌ Framework file not found: {framework_file}")
        return
    
    if not Path(company_plan_file).exists():
        print(f"❌ Company plan file not found: {company_plan_file}")
        return
    
    # Step 1: Extract documents
    print("📄 Extracting BCM Framework...")
    framework_text = extract_text_from_docx(framework_file)
    if not framework_text:
        print("❌ Failed to extract framework document")
        return
    
    print("📄 Extracting Company BCM Plan...")
    company_text = extract_text_from_pdf(company_plan_file)
    if not company_text:
        print("❌ Failed to extract company BCM plan")
        return
    
    print(f"✅ Framework: {len(framework_text)} characters")
    print(f"✅ Company Plan: {len(company_text)} characters")
    
    # Step 2: Extract ISO topics
    print("\n🧠 Analyzing ISO topics from framework...")
    iso_topics = extract_iso_topics(framework_text)
    
    if not iso_topics:
        print("❌ Failed to extract ISO topics")
        return
    
    print(f"✅ Found {len(iso_topics)} ISO topics:")
    for topic in iso_topics.keys():
        print(f"   • {topic}")
    
    # Step 3: Check each topic
    print(f"\n🔍 Checking topic coverage...")
    
    report_lines = []
    report_lines.append("BCM ISO 22301 COMPLIANCE ANALYSIS REPORT")
    report_lines.append("=" * 60)
    report_lines.append("")
    
    for i, (topic, subtopics) in enumerate(iso_topics.items(), 1):
        print(f"   Analyzing {i}/{len(iso_topics)}: {topic}")
        
        analysis = check_topic_coverage(topic, subtopics, company_text)
        
        report_lines.append(f"\n{i}. {topic}")
        report_lines.append("-" * 40)
        report_lines.append(analysis)
        report_lines.append("")
    
    # Step 4: Save report
    report_content = "\n".join(report_lines)
    
    report_file = "bcm_compliance_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n✅ Analysis complete!")
    print(f"📊 Report saved to: {report_file}")
    print(f"📋 Analyzed {len(iso_topics)} ISO topics")
    
    # Show first few lines of report
    print("\n" + "=" * 50)
    print("📋 REPORT PREVIEW:")
    print("=" * 50)
    lines = report_content.split('\n')
    for line in lines[:15]:
        print(line)
    print("...")
    print(f"\n📄 Full report available in: {report_file}")


if __name__ == "__main__":
    main()
