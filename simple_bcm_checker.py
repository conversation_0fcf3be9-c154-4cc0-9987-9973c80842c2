"""
Simple BCM Topic Coverage Checker
"""

import ollama
from pathlib import Path

# Document processing
try:
    from PyPDF2 import PdfReader
    from docx import Document
except ImportError:
    print("Install required packages: pip install PyPDF2 python-docx")
    exit(1)


def read_pdf(file_path):
    """Read PDF file"""
    with open(file_path, 'rb') as file:
        reader = PdfReader(file)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
    return text


def read_docx(file_path):
    """Read Word document"""
    doc = Document(file_path)
    text = ""
    for paragraph in doc.paragraphs:
        text += paragraph.text + "\n"
    return text


def ask_llama(question):
    """Ask LLaMA a question"""
    client = ollama.Client(host='http://localhost:11434')
    response = client.chat(
        model='llama3.2:latest',
        messages=[{'role': 'user', 'content': question}]
    )
    return response['message']['content']


def main():
    print("🔍 Simple BCM Topic Coverage Checker")
    print("=" * 40)
    
    # Read documents
    print("📄 Reading BCM Framework...")
    framework_text = read_docx("BCM_Plan_Framework.docx")
    
    print("📄 Reading Company BCM Plan...")
    company_text = read_pdf("Perpetuuiti_BCM_Plan.pdf")
    
    print(f"✅ Framework: {len(framework_text)} chars")
    print(f"✅ Company Plan: {len(company_text)} chars")
    
    # Step 1: Extract topics from framework
    print("\n🧠 Extracting topics from framework...")
    
    extract_prompt = f"""
    Read this BCM framework document and list all the main topics/sections.
    
    Just give me a simple numbered list like:
    1. Topic Name
    2. Another Topic
    3. etc.
    
    Framework document:
    {framework_text}
    """
    
    topics_response = ask_llama(extract_prompt)
    print("📋 Found topics:")
    print(topics_response)
    
    # Step 2: Check coverage
    print("\n🔍 Checking if topics are covered in company plan...")
    
    coverage_prompt = f"""
    I have a BCM framework with these topics:
    {topics_response}
    
    And I have a company BCM plan:
    {company_text[:3000]}...
    
    For each topic from the framework, tell me if it's covered in the company plan.
    
    Answer in this format:
    ✅ Topic Name - COVERED (brief reason)
    ❌ Topic Name - NOT COVERED (what's missing)
    ⚠️ Topic Name - PARTIALLY COVERED (what needs improvement)
    
    Be specific and practical.
    """
    
    coverage_response = ask_llama(coverage_prompt)
    
    # Display results
    print("\n📊 COVERAGE ANALYSIS:")
    print("=" * 40)
    print(coverage_response)
    
    # Save report
    report = f"""BCM TOPIC COVERAGE REPORT
========================

TOPICS FROM FRAMEWORK:
{topics_response}

COVERAGE ANALYSIS:
{coverage_response}

Generated using LLaMA 3.2
"""
    
    with open("bcm_coverage_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n✅ Report saved to: bcm_coverage_report.txt")


if __name__ == "__main__":
    main()
