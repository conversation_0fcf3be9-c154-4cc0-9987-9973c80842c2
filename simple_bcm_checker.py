"""
Simple BCM Topic Coverage Checker
"""

import ollama
from pathlib import Path

# Document processing
try:
    from PyPDF2 import Pdf<PERSON>eader
    from docx import Document
except ImportError:
    print("Install required packages: pip install PyPDF2 python-docx")
    exit(1)


def read_pdf(file_path):
    """Read PDF file"""
    with open(file_path, 'rb') as file:
        reader = PdfReader(file)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
    return text


def read_docx(file_path):
    """Read Word document"""
    doc = Document(file_path)
    text = ""
    for paragraph in doc.paragraphs:
        text += paragraph.text + "\n"
    return text


def ask_llama(question):
    """Ask LLaMA a question"""
    client = ollama.Client(host='http://***********:11434')
    response = client.chat(
        model='llama3.2:latest',
        messages=[{'role': 'user', 'content': question}]
    )
    return response['message']['content']


def main():
    print("🔍 Simple BCM Topic Coverage Checker")
    print("=" * 40)
    
    # Read documents
    print("📄 Reading BCM Framework...")
    framework_text = read_docx("BCM_Plan_Framework.docx")
    
    print("📄 Reading Company BCM Plan...")
    company_text = read_pdf("Perpetuuiti_BCM_Plan.pdf")
    
    print(f"✅ Framework: {len(framework_text)} chars")
    print(f"✅ Company Plan: {len(company_text)} chars")
    
    # Step 1: Extract topics from framework
    print("\n🧠 Extracting topics from framework...")
    
    extract_prompt = f"""
    Read this BCM framework document and list all the main topics/sections.
    
    Just give me a simple numbered list like:
    1. Topic Name
    2. Another Topic
    3. etc.
    
    Framework document:
    {framework_text}
    """
    
    topics_response = ask_llama(extract_prompt)
    print("📋 Found topics:")
    print(topics_response)
    
    # Step 2: Check coverage by analyzing entire document in chunks
    print("\n🔍 Checking if topics are covered in ENTIRE company plan...")

    # Split company text into chunks for complete analysis
    chunk_size = 3000
    chunks = []
    for i in range(0, len(company_text), chunk_size):
        chunks.append(company_text[i:i + chunk_size])

    print(f"📄 Analyzing {len(chunks)} chunks of company plan...")

    # Analyze each chunk
    all_findings = []
    for i, chunk in enumerate(chunks):
        print(f"   Processing chunk {i+1}/{len(chunks)}...")

        chunk_prompt = f"""
        I have a BCM framework with these topics:
        {topics_response}

        Here is part {i+1} of {len(chunks)} of the company BCM plan:
        {chunk}

        For each topic from the framework, check if it's mentioned or covered in THIS part of the plan.

        Only list topics that you find evidence for in this chunk. Format:
        ✅ Topic Name - FOUND (brief evidence/quote)

        If no topics are found in this chunk, just say "No topics found in this chunk."
        """

        chunk_response = ask_llama(chunk_prompt)
        all_findings.append(f"CHUNK {i+1}:\n{chunk_response}\n")

    # Now do final analysis
    print("🧠 Doing final comprehensive analysis...")

    final_prompt = f"""
    I analyzed a company BCM plan in {len(chunks)} chunks. Here are the findings from each chunk:

    {chr(10).join(all_findings)}

    Based on ALL the findings above, for each topic from the framework:
    {topics_response}

    Give me the final coverage status:
    ✅ Topic Name - COVERED (found in chunk X with evidence)
    ❌ Topic Name - NOT COVERED (not found in any chunk)
    ⚠️ Topic Name - PARTIALLY COVERED (found but incomplete)

    Be thorough and check all chunks.
    """

    coverage_response = ask_llama(final_prompt)
    
    # Display results
    print("\n📊 COVERAGE ANALYSIS:")
    print("=" * 40)
    print(coverage_response)
    
    # Save report
    report = f"""BCM TOPIC COVERAGE REPORT - COMPLETE DOCUMENT ANALYSIS
=====================================================

ANALYSIS DETAILS:
- Company Plan Size: {len(company_text)} characters
- Analyzed in {len(chunks)} chunks of {chunk_size} characters each
- Complete document coverage analysis

TOPICS FROM FRAMEWORK:
{topics_response}

CHUNK-BY-CHUNK FINDINGS:
{chr(10).join(all_findings)}

FINAL COVERAGE ANALYSIS:
{coverage_response}

Generated using LLaMA 3.2 - Complete Document Analysis
"""
    
    with open("bcm_coverage_report.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n✅ Report saved to: bcm_coverage_report.txt")


if __name__ == "__main__":
    main()
